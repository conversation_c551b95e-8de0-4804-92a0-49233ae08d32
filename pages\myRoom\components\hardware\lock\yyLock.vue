<template>
	<view class="deviceItemBox">
		<view class="item1" v-for="item in lock_list" :key="item.id" :style="{background:themeColor.bg_main1_color}"
			@click="openLock(item)" v-if="styleModel==1">
			<view class="item1_title" :style="{color:themeColor.text_title_color}">
				<text>智能门锁</text>
				<view class="open"
					:style="isShow==1?'background-color:'+themeColor.main_color:'background-color: #b5b5b5;'">
					{{isShow==1?'点击开锁':'等待连接'}}
				</view>
			</view>
			<view class="item1_content">
				<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
				<view class="item1_content_text">
					<text :style="{color:themeColor.text_main_color}"
						style="font-weight: 600;">{{lockDetail.lock_alias}}</text>
					<view class="" style="padding-top: 10rpx;display: flex;align-items: center;">
						<p style="width: 10rpx;height: 10rpx;border-radius: 50%;"
							:style="isShow==1?'background-color: #53C21D;':'background-color: #333;'"></p>
						<text style="font-size: 24rpx;padding-left: 6rpx;">{{isShow==1?'在线':'离线'}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class=""
			style="height:300rpx;width: 690rpx;display: flex;align-items: center;justify-content: center;margin: 0 auto;"
			v-if="styleModel==2">
			<swiper :autoplay="false"
				style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
				<swiper-item v-for="(item,index) in lock_list"
					style="height: 100%;align-items: center;justify-content: center;display: flex;">
					<image src="http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/kaisuo_1208.png"
						mode="aspectFill" style="height: 260rpx;width: 260rpx;" @click="openLock(item)">
					</image>
				</swiper-item>
			</swiper>
		</view>
		<view class="" v-if="styleModel==3" style="height: 800rpx;width: 718rpx; background: #FFFFFF;
		border-bottom-left-radius: 32rpx;border-bottom-right-radius: 32rpx;display: flex;align-items: center;">
			<swiper :autoplay="false" style="height: 100%;width: 100%;">
				<swiper-item v-for="(item,index) in lock_list" style="height: 100%;width: 100%;">
					<view class=""
						style="height: 100%;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;position: relative;">
						<view class="" v-if="current==1"
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
							<text style="margin-bottom: 30rpx;">蓝牙开锁</text>
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/lockBg.png"
								style="height: 400rpx;width: 640rpx;" mode="" @click="openLock(item)"></image>
						</view>
						<view class="" v-if="current==2&&item.password"
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
							<text style="margin-bottom: 30rpx;">密码开锁</text>
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/lock.png"
								style="height: 256rpx;width: 176rpx;margin: 70rpx auto;" mode=""></image>
							<p style="color: #000000E0;font-size: 64rpx;letter-spacing:14rpx">{{item.password}}</p>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class=""
								style="height: 80rpx;width: 368rpx;display: flex;align-items: center;background-color: #00000014;border-radius: 16rpx;padding: 0 16rpx;">
								<view class="" v-for="item1 in typeList"
									:style="current==item1.id?'height:60rpx;width:160rpx;border-radius:8rpx;background:#FFFFFF;color:'+themeColor.main_color:''"
									@click="chooseType(item1)" style="width: 50%;height: 100%;display: flex;align-items: center;
								 justify-content: center;font-size: 30rpx;">
									{{item1.name}}
								</view>
							</view>
						</view>
						<view class="" v-if="lock_list.length>1" :style="{color:themeColor.main_color}"
							style="position: absolute;right: 20rpx;top: 20rpx;height: 80rpx;min-width: 100rpx;
						 display: flex;align-items: center;justify-content: center;background-color: #00000014;padding: 0 20rpx;border-radius: 8rpx;">
							门锁{{index+1}}/{{lock_list.length}}
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 锁的弹窗 -->
		<m-popup :show="show" @closePop="closePop">
			<view class="popBox">
				<text style="font-size: 36rpx;font-weight: 600;">通行区域:{{lockDetail.lock_alias}}房门锁</text>
				<view class="box_out">
					<view :class="isOpen==1?'box':'box1'">
						<view class="inner_leange">
							<view class="ball">
							</view>
						</view>
					</view>
					<!-- 正在开门 -->
					<text class="icon-kaisuo overImg" style="font-size: 100rpx;" v-if="isOpen==1"></text>

					<!-- 开门完成 -->
					<image src="/static/images/smile.png" mode="aspectFill" class="overImg1" v-if="isOpen==2">
					</image>

					<!-- 开门失败 -->
					<image src="/static/images/nosmile.png" mode="aspectFill" class="overImg1" v-if="isOpen==3">
					</image>
				</view>

				<text v-if="isOpen==1">授权验证中...</text>

				<text style="font-size: 40rpx;font-weight: 600;" v-if="isOpen==2">请按压门锁把手开门！</text>

				<text style="font-size: 32rpx;color: #FF6600;" v-if="isOpen==4">开锁超时(10秒)，请检查蓝牙设置后重新开锁</text>

				<view class="button-container">
					<u-button type="error" v-if="isOpen==3" style="color:#CD1225;margin-bottom:20rpx;" @click="again">点击重新开锁！</u-button>
					<u-button type="warning" v-if="isOpen==4" style="color:#FF6600;margin-bottom:20rpx;" @click="again">重新开锁</u-button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	const plugin = requirePlugin("yayaLock");
	const bgAudioManager = uni.getBackgroundAudioManager();
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				lockDetail: null,
				show: false,
				isOpen: 0, //1代表正在开门2，开门成功 3,error 4,超时
				isShow: 0, //离线
				authInfo: null,
				typeList: [{
					id: 1,
					name: '蓝牙开锁'
				}, {
					id: 2,
					name: '密码开锁'
				}],
				current: 1,
				lock_list: [],
				openLockTimer: null, // 开锁超时定时器
				isOpeningLock: false // 是否正在开锁中
			};
		},
		props: {
			styleModel: {
				type: [Number, String]
			},
			billDetail: {
				type: Object
			},
		},

		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('room', ['hardWareList']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting'])
		},
		mounted() {
			this.refreshDeviceList()
			console.log(this.hardWareList, this.styleModel, 'dsdsd');
			this.lock_list = this.hardWareList.lock_list.filter(item => {
				return item.lock_type_sign == 'yaya'
			})
		},
		beforeDestroy() {
			// 清除定时器
			if (this.openLockTimer) {
				clearTimeout(this.openLockTimer)
				this.openLockTimer = null
			}
		},
		methods: {
			closePop() {
				this.show = false
				// 清除定时器
				if (this.openLockTimer) {
					clearTimeout(this.openLockTimer)
					this.openLockTimer = null
				}
				this.isOpeningLock = false
			},

			// 检查蓝牙设置
			checkBluetoothSettings() {
				try {
					const systemSetting = wx.getSystemSetting()
					const systemInfo = wx.getDeviceInfo()
					const appAuth = wx.getAppAuthorizeSetting()

					console.log('蓝牙检查 - systemSetting:', systemSetting)
					console.log('蓝牙检查 - systemInfo:', systemInfo)
					console.log('蓝牙检查 - appAuth:', appAuth)

					// 检查蓝牙是否开启
					if (!systemSetting.bluetoothEnabled) {
						this.showBluetoothPermissionModal('蓝牙未开启', '请先打开手机蓝牙，然后点击"重新扫描"按钮！')
						return false
					}

					// 检查定位是否开启
					if (!systemSetting.locationEnabled) {
						this.showBluetoothPermissionModal('定位未开启', '请先打开系统定位服务，然后点击"重新扫描"按钮！')
						return false
					}

					// iOS设备检查蓝牙权限
					if (appAuth.bluetoothAuthorized != 'authorized' && systemInfo.system.includes('iOS')) {
						this.showBluetoothPermissionModal('蓝牙权限未授权', '请允许微信使用蓝牙权限，然后点击"重新扫描"按钮！')
						return false
					}

					// 检查摄像头权限
					if (appAuth.cameraAuthorized != 'authorized') {
						this.showBluetoothPermissionModal('摄像头权限未授权', '请允许微信使用手机的摄像头权限，然后点击"重新扫描"按钮！')
						return false
					}

					// 检查定位权限
					if (appAuth.locationAuthorized != 'authorized') {
						this.showBluetoothPermissionModal('定位权限未授权', '请允许微信使用手机的定位权限，然后点击"重新扫描"按钮！')
						return false
					}

					console.log('蓝牙检查通过')
					return true
				} catch (error) {
					console.error('蓝牙检查出错:', error)
					this.showBluetoothPermissionModal('检查失败', '无法检查蓝牙状态，请确保已开启蓝牙和定位服务，然后点击"重新扫描"按钮！')
					return false
				}
			},

			// 显示蓝牙权限提示弹窗
			showBluetoothPermissionModal(title, content) {
				uni.showModal({
					title: title,
					content: content,
					cancelText: '去设置',
					confirmText: '重新扫描',
					success: (res) => {
						if (res.confirm) {
							// 用户点击"重新扫描"
							this.recheckBluetoothAndScan()
						} else if (res.cancel) {
							// 用户点击"去设置"
							uni.openSetting({
								success: (settingRes) => {
									console.log('设置页面返回:', settingRes)
									// 从设置页面返回后，提示用户重新扫描
									setTimeout(() => {
										uni.showModal({
											title: '提示',
											content: '请点击"重新扫描"按钮重新检测门锁！',
											showCancel: false,
											confirmText: '重新扫描',
											success: () => {
												this.recheckBluetoothAndScan()
											}
										})
									}, 500)
								},
								fail: (err) => {
									console.error('打开设置页面失败:', err)
								}
							})
						}
					}
				})
			},

			// 重新检查蓝牙权限并扫描设备
			recheckBluetoothAndScan() {
				console.log('重新检查蓝牙权限并扫描设备')

				// 重新检查蓝牙设置
				if (!this.checkBluetoothSettings()) {
					return
				}

				// 如果当前没有显示开锁弹窗，需要设置开锁状态
				if (!this.show) {
					this.show = true
					this.isOpen = 1
					this.isOpeningLock = true

					// 设置15秒超时定时器（增加时间以适应重新初始化蓝牙的流程）
					this.openLockTimer = setTimeout(() => {
						if (this.isOpen === 1) { // 如果还在开锁中状态
							this.handleOpenLockTimeout()
						}
					}, 15000)
				}

				// 开始蓝牙重新初始化流程
				this.restartBluetoothAndScan()
			},

			// 重新启动蓝牙并扫描
			restartBluetoothAndScan() {
				console.log('开始重新启动蓝牙流程')

				uni.showLoading({
					title: '正在重新初始化蓝牙...'
				})

				// 第一步：停止蓝牙适配器
				this.stopBluetoothAdapter()
					.then(() => {
						console.log('蓝牙适配器已停止')
						// 等待1秒后重新初始化
						return this.delay(1000)
					})
					.then(() => {
						// 第二步：重新初始化蓝牙适配器
						return this.initBluetoothAdapter()
					})
					.then(() => {
						console.log('蓝牙适配器重新初始化成功')
						uni.hideLoading()
						uni.showLoading({
							title: '正在扫描门锁...'
						})

						// 第三步：开始扫描设备
						this.refreshDeviceList()

						// 扫描5-8秒后开锁（这里设置为6秒）
						setTimeout(() => {
							uni.hideLoading()
							this.attemptToOpenLock()
						}, 6000)
					})
					.catch((error) => {
						console.error('蓝牙重新初始化失败:', error)
						uni.hideLoading()
						uni.showModal({
							title: '蓝牙初始化失败',
							content: '请检查蓝牙设置后重试',
							showCancel: false,
							confirmText: '重新尝试',
							success: () => {
								this.recheckBluetoothAndScan()
							}
						})
					})
			},

			// 停止蓝牙适配器
			stopBluetoothAdapter() {
				return new Promise((resolve, reject) => {
					console.log('正在停止蓝牙适配器...')

					// 先停止搜索
					wx.stopBluetoothDevicesDiscovery({
						success: () => {
							console.log('停止蓝牙设备搜索成功')
						},
						fail: (err) => {
							console.log('停止蓝牙设备搜索失败:', err)
						},
						complete: () => {
							// 无论成功失败都继续关闭蓝牙适配器
							wx.closeBluetoothAdapter({
								success: () => {
									console.log('关闭蓝牙适配器成功')
									resolve()
								},
								fail: (err) => {
									console.log('关闭蓝牙适配器失败:', err)
									// 即使关闭失败也继续流程
									resolve()
								}
							})
						}
					})
				})
			},

			// 初始化蓝牙适配器
			initBluetoothAdapter() {
				return new Promise((resolve, reject) => {
					console.log('正在初始化蓝牙适配器...')

					wx.openBluetoothAdapter({
						success: () => {
							console.log('初始化蓝牙适配器成功')
							resolve()
						},
						fail: (err) => {
							console.error('初始化蓝牙适配器失败:', err)
							reject(err)
						}
					})
				})
			},

			// 延迟函数
			delay(ms) {
				return new Promise(resolve => setTimeout(resolve, ms))
			},

			// 尝试开锁
			attemptToOpenLock() {
				console.log('开始尝试开锁，当前设备状态:', this.isShow)

				// 如果已经检测到锁设备，直接开锁
				if (this.isShow === 1) {
					console.log('检测到门锁，开始开锁')
					this.sysLockTime()
					this.openYYLock()
				} else {
					// 如果还没检测到，继续扫描2秒
					console.log('未检测到门锁，继续扫描...')
					uni.showLoading({
						title: '继续扫描门锁...'
					})

					this.refreshDeviceList()

					setTimeout(() => {
						uni.hideLoading()

						if (this.isShow === 1) {
							console.log('延长扫描后检测到门锁，开始开锁')
							this.sysLockTime()
							this.openYYLock()
						} else {
							// 如果还是没检测到，提示用户
							uni.showModal({
								title: '提示',
								content: '未检测到门锁设备，请确保靠近门锁并重试！',
								showCancel: false,
								confirmText: '重新扫描',
								success: () => {
									this.recheckBluetoothAndScan()
								}
							})
						}
					}, 2000)
				}
			},

			// 处理开锁超时
			handleOpenLockTimeout() {
				console.log('开锁超时，10秒内未成功')
				this.isOpen = 4 // 设置为超时状态
				this.isOpeningLock = false

				// 清除定时器
				if (this.openLockTimer) {
					clearTimeout(this.openLockTimer)
					this.openLockTimer = null
				}

				// 重新检查蓝牙设置
				setTimeout(() => {
					if (!this.checkBluetoothSettings()) {
						// 如果蓝牙检查失败，关闭弹窗
						this.show = false
					}
				}, 1000)
			},
			chooseType(e) {
				if (e.id == 2 && !e.password) {
					uni.showToast({
						icon: 'none',
						title: '此门锁暂无密码',
						duration: 1500
					})
					return
				}
				this.current = e.id
			},
			again() {
				// 重新检查蓝牙设置
				if (!this.checkBluetoothSettings()) {
					this.show = false
					return
				}

				// 重置状态
				this.isOpeningLock = true
				this.isOpen = 1

				// 设置新的10秒超时定时器
				this.openLockTimer = setTimeout(() => {
					if (this.isOpen === 1) { // 如果还在开锁中状态
						this.handleOpenLockTimeout()
					}
				}, 10000)

				// 使用统一的重新扫描方法
				this.performScanAndOpen()
			},

			// 执行扫描并开锁的统一方法
			performScanAndOpen() {
				uni.showLoading({
					title: '正在扫描门锁...'
				})

				// 重新扫描设备
				this.refreshDeviceList()

				setTimeout(() => {
					uni.hideLoading()
					this.openYYLock()
				}, 5000)
			},
			openLock(e) {
				// 防止重复点击
				if (this.isOpeningLock) {
					uni.showToast({
						icon: 'none',
						title: '正在开锁中，请稍候...',
						duration: 1500
					})
					return
				}

				// 判断操作系统
				const systemInfo1 = wx.getSystemInfoSync();
				const isHarmonyOS = systemInfo1.system.includes('HarmonyOS');

				if (isHarmonyOS) {
					wx.startBluetoothDevicesDiscovery({
						success(res) {
							console.log('蓝牙设备发现已开启');
						},
						fail(res) {
							wx.showModal({
								title: '提示',
								content: '请打开“发现附近设备”功能以获得更好的体验。',
								success(res) {
									if (res.confirm) {
										// 用户确认后，跳转到设置页面

									}
								}
							});
						}
					});
				}


				console.log(e, 'k');
				this.lockDetail = e

				// 检查蓝牙设置
				if (!this.checkBluetoothSettings()) {
					return
				}

				// 设置开锁状态
				this.isOpeningLock = true
				this.show = true
				this.isOpen = 1

				// 设置10秒超时定时器
				this.openLockTimer = setTimeout(() => {
					if (this.isOpen === 1) { // 如果还在开锁中状态
						this.handleOpenLockTimeout()
					}
				}, 10000)

				this.sysLockTime()
				// 使用统一的扫描并开锁方法
				this.performScanAndOpen()
				// this.$iBox.http('getRoomBillUser', {
				// 	bill_id: this.billDetail.id
				// })({
				// 	method: 'post'
				// }).then(res2 => {
				// 	this.authInfo = res2.data.filter(item => {
				// 		return item.common_code == this.userInfo.common_code
				// 	})[0]

				// 	let over_time = this.shopSetting.filter(item => {
				// 		return item.sign == 'over_time_blue_tooth_open_door'
				// 	})[0].property.status

				// 	let arrears = this.shopSetting.filter(item => {
				// 		return item.sign == 'arrears_blue_tooth_open_door'
				// 	})[0].property.status

				// 	// 关闭了超时可以开门设置
				// 	if (!over_time) {
				// 		if (this.billDetail.leave_time_plan < this.$moment().unix()) {
				// 			uni.showModal({
				// 				title: '提示',
				// 				content: '入住已超时!',
				// 				showCancel: false,
				// 				success: res => {
				// 					if (res.confirm) {
				// 						uni.switchTab({
				// 							url:'/pages/myRoom/myRoom'
				// 						})
				// 					}
				// 				}
				// 			})
				// 			return
				// 		}
				// 	}
				// 	// 关闭了欠费可以开门设置
				// 	if (!arrears) {
				// 		if (this.billDetail.already_pay - this.billDetail.bill_amount < 0) {
				// 			uni.showModal({
				// 				title: '提示',
				// 				content: '订单已欠费!',
				// 				showCancel: false,
				// 				success: res => {
				// 					if (res.confirm) {
				// 						uni.switchTab({
				// 							url:'/pages/myRoom/myRoom'
				// 						})
				// 					}
				// 				}
				// 			})
				// 			return
				// 		}

				// 	}

				// 	// 判断是否有身份证记录,有的话则直接展示
				// 	if ((this.authInfo && this.authInfo.user_status == 1 && this.billDetail.bill_status == 4) || (
				// 			this.billDetail.bill_status == 4 && !this.authInfo.is_main)) {
				// 		this.openYYLock()
				// 		this.show = true
				// 		this.isOpen = 1
				// 	} else {
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '您已退房或无开门权限',
				// 			showCancel: false,
				// 			success: res => {
				// 				if (res.confirm) {
				// 					uni.switchTab({
				// 						url:'/pages/myRoom/myRoom'
				// 					})
				// 				}
				// 			}
				// 		})
				// 	}

				// })
			},
			//-----------------------------------------------------丫丫锁--------------------
			refreshDeviceList() {
				var that = this;
				console.log("开锁扫描");
				plugin.scanSmartLock(that.scanDeviceCallBack);
				//scanRefreshLock with params scanTime in millisecond and scanCallBack
				//plugin.scanRefreshLock(15000,this.scanDeviceCallBack)
			},
			/**
			 * 扫描设备回调
			 */
			scanDeviceCallBack(res) {
				var that = this;
				var dev = res.data.msg
				var deviceName = dev.localName
				let macname = dev.localName.slice(2, 14)
				let mac = ""
				for (let i = 0, len = macname.length; i < len; i++) {
					mac += macname[i];
					if (i % 2 == 1 && i <= len - 2) mac += ":";
				}
				console.log("丫丫锁: " + deviceName);
				console.log("scanDeviceCallBack: " + deviceName);
				let rssi = dev.RSSI
				let lockId = plugin.parseDeviceId(deviceName);
				let deviceType = plugin.parseDeviceType(deviceName);
				let devName = plugin.parseDeviceName(deviceName);
				let devMid = dev.deviceId;
				let lock = {
					"LOCKID": lockId,
					"RSSI": rssi,
					"LOCKNAME": mac,
					"DATA": {
						KEYLOCKID: lockId,
						KEYUSERNAME: devName,
						DEVICETYPE: deviceType,
						DEVMID: devMid
					}
				};
				if (this.lockDetail && this.lockDetail.lock_name == lockId) {
					this.isShow = 1
				}

			},

			// 校准锁时间
			sysLockTime(e) {
				var that = this;
				var deviceId = this.lockDetail.device_id;
				var devicePsw = '23458892D88C71D5AD78F3C6CF933725';
				console.log('112', deviceId);
				var tStr = plugin.buildSysLockTime(deviceId, devicePsw);
				console.log('113');
				plugin.operateLockNoLoading(deviceId, "", 11, tStr, function(res) {

					if (res.data.result == 200) {
						var openResult = plugin.parseReceiveData(res.data.msg);
					}
				});
			},

			// 点击开锁
			openYYLock(e) {
				console.log(e, 'kl');
				// this.getWxAuthorizeLocation()
				let that = this
				let a = this.lockDetail.device_id.split(":")
				let deviceId = a[2] + a[3] + a[4] + a[5];

				let devicePsw = '23458892D88C71D5AD78F3C6CF933725'

				let tStr = plugin.buildOpenDeviceO2O(deviceId, devicePsw);

				plugin.operateLockNoLoading(deviceId, "", 11, tStr, function(res) {
					var openResult = plugin.parseReceiveData(res.data.msg);

					// 清除超时定时器
					if (that.openLockTimer) {
						clearTimeout(that.openLockTimer)
						that.openLockTimer = null
					}

					if (res.data.result == 200) {
						that.isOpen = 2
						that.isOpeningLock = false

						// 开锁语音
						bgAudioManager.title = '提醒'
						bgAudioManager.epname = '提醒'
						bgAudioManager.singer = '提醒'
						bgAudioManager.src =
							'https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E5%BC%80%E9%97%A8.mp3'

						// 上传日志
						let param = {
							lock_id: that.lockDetail.id,
							electric_quantity: openResult.battery ? openResult.battery : 0
						}
						that.$iBox
							.http('addRoomYayaOpenDoorRecord', param)({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '开锁成功！'
								})
								uni.hideLoading()
							}).catch(err => {
								uni.hideLoading()
							})
					} else {
						that.isOpen = 3
						that.isOpeningLock = false
					}
				})

				// plugin.connectDevice(deviceId, function(res) {
				// 	uni.showLoading({
				// 		title: '正在连接门锁中'
				// 	});
				// 	if (res.data.result == 0) {
				// 		uni.hideLoading();

				// 	}else{
				// 		that.isOpen = 3
				// 		uni.showToast({
				// 			icon:'error',
				// 			title:'门锁连接失败'
				// 		})
				// 	}
				// });
			},
			//===========================================权限验证=================================================
			getWxAuthorizeLocation() {
				uni.getSetting({
					success: (res) => {

						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							uni.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作

								},
								fail() {
									uni.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})

								}
							})
							return
						}

						if (res.authSetting['scope.bluetooth'] == null) {
							uni.authorize({
								scope: 'scope.bluetooth',
								success() {
									// 用户同意
									// 相关操作
								},
								fail() {
									uni.showToast({
										title: '无法申请定位权限,请确认是否已经授权蓝牙权限',
										icon: "none",
										duration: 2000
									})
								}
							})
							return
						}

						// 如果已经有权限，就查询
						if (res.authSetting['scope.userLocation'] == true && res.authSetting[
								'scope.bluetooth']) {
							// 相关操作
						} else { // 被拒绝过授权，重新申请
							uni.showModal({
								title: '信息授权',
								content: '蓝牙权暂未开启，将导致无法正常认证',
								cancelText: '仍然拒绝',
								confirmText: '开启授权',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting({
											fail: function() {

											}
										})
									} else {
										uni.openSetting({
											fail: function() {

											}
										})
									}
								}
							})
						}
					}
				});
			},
		}

	}
</script>

<style lang="scss" scoped>
	.deviceItemBox {

		// display: flex;
		// align-items: center;
		// justify-content: space-between;
		// flex-wrap: wrap;
		// padding: 0 30rpx;
		// width: 100%;
		.item1 {
			height: 180rpx;
			width: 330rpx;
			margin-top: 30rpx;
			border-radius: 20rpx;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

			&_title {
				padding: 20rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.open {
					width: fit-content;
					padding: 10rpx 16rpx;
					border-radius: 30rpx;
					color: #ffffff;
					// background-color: rgba(0, 0, 0, 0.6);
					font-size: 22rpx;
				}
			}

			&_content {
				padding: 0rpx 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.img_lock {
					height: 80rpx;
					width: 80rpx;
				}

				&_text {
					display: flex;
					flex-direction: column;
				}
			}
		}

		.popBox {
			height: 880rpx;
			padding: 30rpx 30rpx 100rpx 30rpx; /* 增加底部内边距避免被tabbar遮挡 */
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			box-sizing: border-box;

			@keyframes spin {

				from {
					transform: rotate(0deg);
				}

				to {
					transform: rotate(360deg);
				}

			}

			@keyframes scbig {

				from {
					transform: scale(0, 0);
				}

				to {
					transform: scale(1, 1);
				}

			}

			.overImg {
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.overImg1 {
				height: 100rpx;
				width: 120rpx;
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.box_out {
				height: 320rpx;
				width: 320rpx;
				border-radius: 50%;
				background: #dddddd;
				display: flex;
				align-items: center;
				justify-content: center;

				.box {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;
						animation: spin 3s infinite linear;

						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}

				.box1 {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;


						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}
			}

			.button-container {
				width: 100%;
				padding: 0 20rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: center;
				
			}

		}
	}
</style>